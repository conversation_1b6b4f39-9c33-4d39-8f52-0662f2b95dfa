#!/usr/bin/env python3
"""
Test A.T.L.A.S. conversational intelligence to ensure appropriate responses
"""
import requests
import json
import time
from datetime import datetime

def test_conversational_intelligence():
    """Test that A.T.L.A.S. responds appropriately to different message types"""
    
    url = "http://localhost:8080/api/v1/chat"
    
    test_cases = [
        {
            "name": "Simple Greeting",
            "message": "hello",
            "expected_type": "greeting",
            "should_not_contain": ["**A.T.L.A.S powered by Predicto - Stock Market God**", "Entry Strategy:", "Position value:"],
            "should_contain": ["Hello!", "A.T.L.A.S.", "What would you like to explore"]
        },
        {
            "name": "Help Request",
            "message": "What can you do?",
            "expected_type": "capabilities",
            "should_not_contain": ["**A.T.L.A.S powered by Predicto - Stock Market God**", "Entry Strategy:", "Position value:"],
            "should_contain": ["capabilities", "trading", "analysis"]
        },
        {
            "name": "General Question",
            "message": "How are you?",
            "expected_type": "general_conversation",
            "should_not_contain": ["**A.T.L.A.S powered by Predicto - Stock Market God**", "Entry Strategy:", "Position value:"],
            "should_contain": ["great", "trading", "help"]
        },
        {
            "name": "Stock Analysis Request",
            "message": "Analyze AAPL for a potential trade",
            "expected_type": "guru_trade_plan",
            "should_contain": ["**A.T.L.A.S powered by Predicto - Stock Market God**", "**1. Why This Trade?**", "**6. Confidence Score**"],
            "should_not_contain": ["Hello!", "What would you like to explore"]
        },
        {
            "name": "Trading Goal Request",
            "message": "I want to make $200 today",
            "expected_type": "guru_trade_plan",
            "should_contain": ["**A.T.L.A.S powered by Predicto - Stock Market God**", "**1. Why This Trade?**"],
            "should_not_contain": ["Hello!", "What would you like to explore"]
        }
    ]
    
    print("🧠 A.T.L.A.S. Conversational Intelligence Test")
    print("=" * 60)
    print(f"Testing at: {datetime.now()}")
    print(f"Endpoint: {url}")
    print()
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"📝 Test {i}: {test_case['name']}")
        print(f"Message: '{test_case['message']}'")
        print("-" * 50)
        
        payload = {
            "message": test_case["message"],
            "session_id": f"conv_test_{i}",
            "user_id": "test_user",
            "context": {
                "interface": "conversational_test",
                "timestamp": datetime.now().isoformat()
            }
        }
        
        try:
            start_time = time.time()
            response = requests.post(url, json=payload, timeout=30)
            end_time = time.time()
            
            response_time = end_time - start_time
            
            print(f"⏱️  Response time: {response_time:.2f}s")
            print(f"📊 Status code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '')
                response_type = data.get('type', 'unknown')
                confidence = data.get('confidence', 0.0)
                
                print(f"✅ Response received")
                print(f"📄 Type: {response_type}")
                print(f"🎯 Confidence: {confidence}")
                print(f"📏 Length: {len(response_text)} characters")
                
                # Check response type
                type_correct = response_type == test_case['expected_type']
                print(f"🎯 Expected type: {test_case['expected_type']}")
                print(f"🎯 Actual type: {response_type}")
                print(f"✅ Type match: {type_correct}")
                
                # Check content requirements
                should_contain_found = []
                should_not_contain_found = []
                
                for phrase in test_case.get('should_contain', []):
                    if phrase in response_text:
                        should_contain_found.append(phrase)
                
                for phrase in test_case.get('should_not_contain', []):
                    if phrase in response_text:
                        should_not_contain_found.append(phrase)
                
                should_contain_score = len(should_contain_found) / len(test_case.get('should_contain', [1])) if test_case.get('should_contain') else 1.0
                should_not_contain_score = 1.0 - (len(should_not_contain_found) / len(test_case.get('should_not_contain', [1])) if test_case.get('should_not_contain') else 0.0)
                
                print(f"✅ Should contain: {len(should_contain_found)}/{len(test_case.get('should_contain', []))}")
                print(f"❌ Should NOT contain: {len(should_not_contain_found)}/{len(test_case.get('should_not_contain', []))}")
                
                # Overall score
                overall_score = (int(type_correct) + should_contain_score + should_not_contain_score) / 3
                
                if overall_score >= 0.8:
                    result_status = "PASS"
                    print(f"✅ PASS - Score: {overall_score:.2%}")
                else:
                    result_status = "FAIL"
                    print(f"❌ FAIL - Score: {overall_score:.2%}")
                
                # Show response preview
                preview_length = 200
                preview = response_text[:preview_length] + "..." if len(response_text) > preview_length else response_text
                print(f"\n💬 Response Preview:")
                print(f"'{preview}'")
                
                # Store result
                results.append({
                    "test": test_case['name'],
                    "status": result_status,
                    "score": overall_score,
                    "type_correct": type_correct,
                    "expected_type": test_case['expected_type'],
                    "actual_type": response_type,
                    "should_contain_score": should_contain_score,
                    "should_not_contain_score": should_not_contain_score,
                    "response_time": response_time,
                    "confidence": confidence,
                    "response_length": len(response_text)
                })
                
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                print(f"Response: {response.text}")
                results.append({
                    "test": test_case['name'],
                    "status": "ERROR",
                    "error": f"HTTP {response.status_code}"
                })
                
        except Exception as e:
            print(f"❌ Error: {e}")
            results.append({
                "test": test_case['name'],
                "status": "ERROR",
                "error": str(e)
            })
        
        print("\n" + "="*60 + "\n")
        time.sleep(2)  # Small delay between tests
    
    # Summary
    print("📊 CONVERSATIONAL INTELLIGENCE TEST SUMMARY")
    print("=" * 60)
    
    pass_count = sum(1 for r in results if r.get('status') == 'PASS')
    fail_count = sum(1 for r in results if r.get('status') == 'FAIL')
    error_count = sum(1 for r in results if r.get('status') == 'ERROR')
    
    total_tests = len(results)
    
    print(f"Total Tests: {total_tests}")
    print(f"✅ Passed: {pass_count}")
    print(f"❌ Failed: {fail_count}")
    print(f"🔥 Errors: {error_count}")
    
    success_rate = (pass_count / total_tests) * 100 if total_tests > 0 else 0
    print(f"\n🎯 Success Rate: {success_rate:.1f}%")
    
    # Detailed analysis
    conversational_tests = [r for r in results if r.get('test') in ['Simple Greeting', 'Help Request', 'General Question']]
    trading_tests = [r for r in results if r.get('test') in ['Stock Analysis Request', 'Trading Goal Request']]
    
    conv_pass = sum(1 for r in conversational_tests if r.get('status') == 'PASS')
    trading_pass = sum(1 for r in trading_tests if r.get('status') == 'PASS')
    
    print(f"\n📋 Detailed Analysis:")
    print(f"💬 Conversational responses: {conv_pass}/{len(conversational_tests)} passed")
    print(f"📈 Trading responses: {trading_pass}/{len(trading_tests)} passed")
    
    if success_rate >= 80:
        print("🎉 EXCELLENT: Conversational intelligence is working properly!")
    elif success_rate >= 60:
        print("👍 GOOD: Most responses are appropriate, minor issues to address")
    else:
        print("❌ CRITICAL: Conversational intelligence needs fixing")
    
    # Save detailed results
    with open("conversational_test_results.json", "w") as f:
        json.dump({
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": total_tests,
                "passed": pass_count,
                "failed": fail_count,
                "errors": error_count,
                "success_rate": success_rate,
                "conversational_pass_rate": (conv_pass / len(conversational_tests)) * 100 if conversational_tests else 0,
                "trading_pass_rate": (trading_pass / len(trading_tests)) * 100 if trading_tests else 0
            },
            "detailed_results": results
        }, f, indent=2)
    
    print(f"\n💾 Detailed results saved to 'conversational_test_results.json'")
    
    return success_rate >= 80

if __name__ == "__main__":
    success = test_conversational_intelligence()
    exit(0 if success else 1)
