#!/usr/bin/env python3
"""
Check the status of Predicto engine and other components
"""
import requests
import json

def check_system_status():
    """Check the initialization status of all components"""
    
    print("🔍 Checking A.T.L.A.S. System Status")
    print("=" * 50)
    
    # Check health endpoint
    try:
        health_url = "http://localhost:8080/api/v1/health"
        print(f"📡 Checking health endpoint: {health_url}")
        
        response = requests.get(health_url, timeout=10)
        print(f"📊 Health Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Health check successful!")
            
            # Show system status
            if 'status' in data:
                print(f"🏥 Overall Status: {data['status']}")
            
            # Show component statuses
            if 'engines' in data:
                print("\n🔧 Component Status:")
                engines = data['engines']
                for component, status in engines.items():
                    status_icon = "✅" if status == "ACTIVE" else "⚠️" if status == "INITIALIZING" else "❌"
                    print(f"  {status_icon} {component}: {status}")
            
            # Show initialization details
            if 'initialization' in data:
                print(f"\n🚀 Initialization: {data['initialization']}")
            
            # Show any errors
            if 'errors' in data and data['errors']:
                print(f"\n❌ Errors: {data['errors']}")
            
            return data
        else:
            print(f"❌ Health check failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return None

def check_initialization_status():
    """Check detailed initialization status"""
    
    try:
        init_url = "http://localhost:8080/api/v1/initialization/status"
        print(f"\n📡 Checking initialization status: {init_url}")
        
        response = requests.get(init_url, timeout=10)
        print(f"📊 Initialization Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Initialization status retrieved!")
            
            # Show detailed component status
            if 'components' in data:
                print("\n🔧 Detailed Component Status:")
                for component, details in data['components'].items():
                    status = details.get('status', 'UNKNOWN')
                    progress = details.get('progress', 0)
                    status_icon = "✅" if status == "ACTIVE" else "⚠️" if status == "INITIALIZING" else "❌"
                    print(f"  {status_icon} {component}: {status} ({progress:.1f}%)")
            
            return data
        else:
            print(f"❌ Initialization check failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Initialization check error: {e}")
        return None

def test_predicto_capabilities():
    """Test if Predicto capabilities endpoint works"""
    
    try:
        cap_url = "http://localhost:8080/api/v1/predicto/capabilities"
        print(f"\n📡 Checking Predicto capabilities: {cap_url}")
        
        response = requests.get(cap_url, timeout=10)
        print(f"📊 Capabilities Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Predicto capabilities retrieved!")
            
            if 'capabilities' in data:
                print(f"\n🎯 Available Capabilities: {len(data['capabilities'])}")
                for cap in data['capabilities'][:5]:  # Show first 5
                    print(f"  • {cap}")
                if len(data['capabilities']) > 5:
                    print(f"  ... and {len(data['capabilities']) - 5} more")
            
            return data
        else:
            print(f"❌ Capabilities check failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Capabilities check error: {e}")
        return None

if __name__ == "__main__":
    print("🚀 A.T.L.A.S. System Diagnostic")
    print("=" * 60)
    
    # Run all checks
    health_data = check_system_status()
    init_data = check_initialization_status()
    cap_data = test_predicto_capabilities()
    
    print("\n" + "=" * 60)
    print("🏁 Diagnostic Complete!")
    
    # Summary
    if health_data and health_data.get('status') == 'healthy':
        print("✅ System is healthy and ready")
    else:
        print("⚠️ System may have issues")
    
    # Check if Predicto is working
    if (health_data and 
        health_data.get('engines', {}).get('ai_engine') == 'ACTIVE'):
        print("✅ Predicto should be working")
    else:
        print("❌ Predicto may not be working properly")
