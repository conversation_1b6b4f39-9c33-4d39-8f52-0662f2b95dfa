#!/usr/bin/env python3
"""
Final validation of A.T.L.A.S. conversational intelligence fixes
"""
import requests
import json
import time
from datetime import datetime

def final_validation():
    """Final comprehensive test of conversational intelligence"""
    
    url = "http://localhost:8080/api/v1/chat"
    
    test_cases = [
        {
            "category": "Conversational",
            "message": "hello",
            "expected_type": "greeting",
            "expected_persona": "conversational",
            "should_contain": ["Hello!", "A.T.L.A.S.", "What would you like to explore"],
            "should_not_contain": ["**A.T.L.A.S powered by Predicto - Stock Market God**", "Entry Strategy:", "Position value:"]
        },
        {
            "category": "Conversational", 
            "message": "What can you do?",
            "expected_type": "capabilities",
            "expected_persona": "conversational",
            "should_contain": ["capabilities", "trading", "analysis"],
            "should_not_contain": ["**A.T.L.A.S powered by Predicto - Stock Market God**", "Entry Strategy:", "Position value:"]
        },
        {
            "category": "Conversational",
            "message": "How are you?",
            "expected_type": "general_conversation",
            "expected_persona": "conversational", 
            "should_contain": ["great", "trading", "help"],
            "should_not_contain": ["**A.T.L.A.S powered by Predicto - Stock Market God**", "Entry Strategy:", "Position value:"]
        },
        {
            "category": "Trading",
            "message": "Analyze AAPL for a potential trade",
            "expected_type": "guru_trade_plan",
            "expected_persona": "guru",
            "should_contain": ["**A.T.L.A.S powered by Predicto - Stock Market God**", "**1. Why This Trade?**", "**6. Confidence Score**"],
            "should_not_contain": ["Hello!", "What would you like to explore"]
        },
        {
            "category": "Trading",
            "message": "I want to make $200 today",
            "expected_type": "guru_trade_plan", 
            "expected_persona": "guru",
            "should_contain": ["**A.T.L.A.S powered by Predicto - Stock Market God**", "**1. Why This Trade?**"],
            "should_not_contain": ["Hello!", "What would you like to explore"]
        }
    ]
    
    print("🎯 A.T.L.A.S. Conversational Intelligence - Final Validation")
    print("=" * 70)
    print(f"Testing at: {datetime.now()}")
    print(f"Endpoint: {url}")
    print()
    
    results = []
    conversational_passed = 0
    trading_passed = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"📝 Test {i}: {test_case['category']} - '{test_case['message']}'")
        print("-" * 60)
        
        payload = {
            "message": test_case["message"],
            "session_id": f"final_test_{i}",
            "user_id": "validation_user"
        }
        
        try:
            start_time = time.time()
            response = requests.post(url, json=payload, timeout=20)
            end_time = time.time()
            
            response_time = end_time - start_time
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '')
                response_type = data.get('type', 'unknown')
                confidence = data.get('confidence', 0.0)
                
                # Check response type
                type_correct = response_type == test_case['expected_type']
                
                # Check content requirements
                should_contain_found = sum(1 for phrase in test_case.get('should_contain', []) if phrase in response_text)
                should_not_contain_found = sum(1 for phrase in test_case.get('should_not_contain', []) if phrase in response_text)
                
                should_contain_score = should_contain_found / len(test_case.get('should_contain', [1])) if test_case.get('should_contain') else 1.0
                should_not_contain_score = 1.0 - (should_not_contain_found / len(test_case.get('should_not_contain', [1])) if test_case.get('should_not_contain') else 0.0)
                
                # Overall score
                overall_score = (int(type_correct) + should_contain_score + should_not_contain_score) / 3
                
                # Determine pass/fail
                passed = overall_score >= 0.8
                status = "✅ PASS" if passed else "❌ FAIL"
                
                print(f"{status} - Score: {overall_score:.1%}")
                print(f"📊 Type: {response_type} (expected: {test_case['expected_type']}) {'✅' if type_correct else '❌'}")
                print(f"📏 Length: {len(response_text)} chars")
                print(f"⏱️  Time: {response_time:.2f}s")
                print(f"🎯 Confidence: {confidence}")
                print(f"✅ Contains required: {should_contain_found}/{len(test_case.get('should_contain', []))}")
                print(f"❌ Contains forbidden: {should_not_contain_found}/{len(test_case.get('should_not_contain', []))}")
                
                # Track category performance
                if test_case['category'] == 'Conversational' and passed:
                    conversational_passed += 1
                elif test_case['category'] == 'Trading' and passed:
                    trading_passed += 1
                
                # Show response preview
                preview = response_text[:150] + "..." if len(response_text) > 150 else response_text
                print(f"\n💬 Preview: {preview}")
                
                results.append({
                    "test": f"{test_case['category']}: {test_case['message'][:30]}...",
                    "status": "PASS" if passed else "FAIL",
                    "score": overall_score,
                    "category": test_case['category'],
                    "type_correct": type_correct,
                    "expected_type": test_case['expected_type'],
                    "actual_type": response_type
                })
                
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                results.append({
                    "test": f"{test_case['category']}: {test_case['message'][:30]}...",
                    "status": "ERROR",
                    "error": f"HTTP {response.status_code}"
                })
                
        except Exception as e:
            print(f"❌ Error: {e}")
            results.append({
                "test": f"{test_case['category']}: {test_case['message'][:30]}...",
                "status": "ERROR",
                "error": str(e)
            })
        
        print("\n" + "="*70 + "\n")
        time.sleep(1)
    
    # Final Summary
    print("🏆 FINAL VALIDATION SUMMARY")
    print("=" * 70)
    
    total_tests = len(test_cases)
    total_passed = sum(1 for r in results if r.get('status') == 'PASS')
    total_failed = sum(1 for r in results if r.get('status') == 'FAIL')
    total_errors = sum(1 for r in results if r.get('status') == 'ERROR')
    
    conversational_total = sum(1 for tc in test_cases if tc['category'] == 'Conversational')
    trading_total = sum(1 for tc in test_cases if tc['category'] == 'Trading')
    
    print(f"📊 Overall Results:")
    print(f"   Total Tests: {total_tests}")
    print(f"   ✅ Passed: {total_passed}")
    print(f"   ❌ Failed: {total_failed}")
    print(f"   🔥 Errors: {total_errors}")
    print(f"   🎯 Success Rate: {(total_passed/total_tests)*100:.1f}%")
    
    print(f"\n📋 Category Breakdown:")
    print(f"   💬 Conversational: {conversational_passed}/{conversational_total} passed ({(conversational_passed/conversational_total)*100:.1f}%)")
    print(f"   📈 Trading: {trading_passed}/{trading_total} passed ({(trading_passed/trading_total)*100:.1f}%)")
    
    # Final verdict
    success_rate = (total_passed / total_tests) * 100
    conversational_rate = (conversational_passed / conversational_total) * 100 if conversational_total > 0 else 0
    trading_rate = (trading_passed / trading_total) * 100 if trading_total > 0 else 0
    
    print(f"\n🎯 FINAL VERDICT:")
    if success_rate >= 90 and conversational_rate >= 90 and trading_rate >= 90:
        print("🎉 EXCELLENT: Conversational intelligence is working perfectly!")
        print("✅ Greetings and help requests get appropriate conversational responses")
        print("✅ Trading requests get sophisticated Stock Market God responses")
        print("✅ System correctly distinguishes between conversation types")
    elif success_rate >= 70:
        print("👍 GOOD: System is mostly working with minor issues")
    else:
        print("❌ NEEDS WORK: Conversational intelligence requires further fixes")
    
    return success_rate >= 90

if __name__ == "__main__":
    success = final_validation()
    exit(0 if success else 1)
