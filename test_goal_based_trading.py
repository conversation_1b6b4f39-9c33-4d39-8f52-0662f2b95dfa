#!/usr/bin/env python3
"""
Test goal-based trading detection
"""
import requests
import json

def test_goal_trading():
    url = "http://localhost:8080/api/v1/chat"
    
    payload = {
        "message": "I want to make $200 today",
        "session_id": "goal_test"
    }
    
    try:
        response = requests.post(url, json=payload, timeout=10)
        if response.status_code == 200:
            data = response.json()
            response_type = data.get('type', 'unknown')
            response_text = data.get('response', '')
            
            print(f"Message: 'I want to make $200 today'")
            print(f"Response Type: {response_type}")
            print(f"Expected: guru_trade_plan")
            print(f"Match: {'✅' if response_type == 'guru_trade_plan' else '❌'}")
            print(f"Preview: {response_text[:150]}...")
            
            if "**A.T.L.A.S powered by Predicto - Stock Market God**" in response_text:
                print("✅ Trading God format detected")
            else:
                print("❌ Trading God format NOT detected")
                
        else:
            print(f"Error: {response.status_code}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_goal_trading()
