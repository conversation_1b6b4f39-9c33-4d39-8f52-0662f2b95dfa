#!/usr/bin/env python3
"""
Comprehensive test to validate A.T.L.A.S. sophisticated response generation
"""
import requests
import json
import time
from datetime import datetime

def test_sophisticated_responses():
    """Test that A.T.L.A.S. generates sophisticated 6-point format responses"""
    
    url = "http://localhost:8080/api/v1/chat"
    
    test_cases = [
        {
            "name": "Stock Analysis Request",
            "message": "Analyze AAPL for a potential trade",
            "expected_format": "sophisticated",
            "expected_markers": [
                "**A.T.L.A.S powered by Predicto - Stock Market God**",
                "**1. Why This Trade?**",
                "**2. Win/Loss Probabilities**",
                "**3. Potential Money In or Out**",
                "**4. Smart Stop Plans**",
                "**5. Market Context**",
                "**6. Confidence Score**"
            ]
        },
        {
            "name": "Goal-Based Trading",
            "message": "I want to make $200 today, what are my best options?",
            "expected_format": "sophisticated",
            "expected_markers": [
                "**A.T.L.A.S powered by Predicto - Stock Market God**",
                "**1. Why This Trade?**",
                "**6. Confidence Score**"
            ]
        },
        {
            "name": "Market Analysis",
            "message": "What's the best trade setup right now?",
            "expected_format": "sophisticated",
            "expected_markers": [
                "**A.T.L.A.S powered by Predicto - Stock Market God**",
                "**1. Why This Trade?**"
            ]
        }
    ]
    
    print("🎯 A.T.L.A.S. Sophisticated Response Validation")
    print("=" * 60)
    print(f"Testing at: {datetime.now()}")
    print(f"Endpoint: {url}")
    print()
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"📝 Test {i}: {test_case['name']}")
        print(f"Message: '{test_case['message']}'")
        print("-" * 50)
        
        payload = {
            "message": test_case["message"],
            "session_id": f"test_sophisticated_{i}",
            "user_id": "test_user",
            "context": {
                "interface": "validation_test",
                "timestamp": datetime.now().isoformat()
            }
        }
        
        try:
            start_time = time.time()
            response = requests.post(url, json=payload, timeout=30)
            end_time = time.time()
            
            response_time = end_time - start_time
            
            print(f"⏱️  Response time: {response_time:.2f}s")
            print(f"📊 Status code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '')
                response_type = data.get('type', 'unknown')
                confidence = data.get('confidence', 0.0)
                
                print(f"✅ Response received")
                print(f"📄 Type: {response_type}")
                print(f"🎯 Confidence: {confidence}")
                print(f"📏 Length: {len(response_text)} characters")
                
                # Check for sophisticated format markers
                found_markers = []
                for marker in test_case['expected_markers']:
                    if marker in response_text:
                        found_markers.append(marker)
                
                marker_score = len(found_markers) / len(test_case['expected_markers'])
                
                print(f"🔍 Format Analysis:")
                print(f"  Expected markers: {len(test_case['expected_markers'])}")
                print(f"  Found markers: {len(found_markers)}")
                print(f"  Marker score: {marker_score:.2%}")
                
                # Determine if response is sophisticated
                is_sophisticated = marker_score >= 0.5  # At least 50% of markers found
                
                if is_sophisticated:
                    print(f"✅ SOPHISTICATED FORMAT DETECTED")
                    result_status = "PASS"
                else:
                    print(f"❌ SOPHISTICATED FORMAT NOT DETECTED")
                    result_status = "FAIL"
                
                # Check for fallback indicators
                if "[FALLBACK MODE]" in response_text:
                    print(f"⚠️  FALLBACK MODE DETECTED - API may not be working")
                    result_status = "FALLBACK"
                
                # Show response preview
                preview_length = 200
                preview = response_text[:preview_length] + "..." if len(response_text) > preview_length else response_text
                print(f"\n💬 Response Preview:")
                print(f"'{preview}'")
                
                # Store result
                results.append({
                    "test": test_case['name'],
                    "status": result_status,
                    "marker_score": marker_score,
                    "response_time": response_time,
                    "response_type": response_type,
                    "confidence": confidence,
                    "response_length": len(response_text)
                })
                
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                print(f"Response: {response.text}")
                results.append({
                    "test": test_case['name'],
                    "status": "ERROR",
                    "error": f"HTTP {response.status_code}"
                })
                
        except Exception as e:
            print(f"❌ Error: {e}")
            results.append({
                "test": test_case['name'],
                "status": "ERROR",
                "error": str(e)
            })
        
        print("\n" + "="*60 + "\n")
        time.sleep(2)  # Small delay between tests
    
    # Summary
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    pass_count = sum(1 for r in results if r.get('status') == 'PASS')
    fail_count = sum(1 for r in results if r.get('status') == 'FAIL')
    fallback_count = sum(1 for r in results if r.get('status') == 'FALLBACK')
    error_count = sum(1 for r in results if r.get('status') == 'ERROR')
    
    total_tests = len(results)
    
    print(f"Total Tests: {total_tests}")
    print(f"✅ Passed: {pass_count}")
    print(f"❌ Failed: {fail_count}")
    print(f"⚠️  Fallback: {fallback_count}")
    print(f"🔥 Errors: {error_count}")
    
    success_rate = (pass_count / total_tests) * 100 if total_tests > 0 else 0
    print(f"\n🎯 Success Rate: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🎉 EXCELLENT: A.T.L.A.S. is generating sophisticated responses!")
    elif success_rate >= 60:
        print("👍 GOOD: A.T.L.A.S. is mostly working, minor issues to address")
    elif fallback_count > 0:
        print("⚠️  WARNING: System is falling back to hardcoded responses")
    else:
        print("❌ CRITICAL: A.T.L.A.S. sophisticated responses not working")
    
    # Save detailed results
    with open("response_validation_results.json", "w") as f:
        json.dump({
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": total_tests,
                "passed": pass_count,
                "failed": fail_count,
                "fallback": fallback_count,
                "errors": error_count,
                "success_rate": success_rate
            },
            "detailed_results": results
        }, f, indent=2)
    
    print(f"\n💾 Detailed results saved to 'response_validation_results.json'")
    
    return success_rate >= 80

if __name__ == "__main__":
    success = test_sophisticated_responses()
    exit(0 if success else 1)
